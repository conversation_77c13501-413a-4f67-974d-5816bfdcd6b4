#!/usr/bin/env python3
"""
简单的LightStereo ONNX推理脚本
使用指定的模型: lightstereo_s_sceneflow_general_opt_576_960.onnx
"""

import cv2
import numpy as np
import onnxruntime as ort
import argparse
import os
import time

def preprocess_images(left_img, right_img, target_height=576, target_width=960):
    """
    预处理立体图像对
    """
    # 调整图像大小
    left_resized = cv2.resize(left_img, (target_width, target_height))
    right_resized = cv2.resize(right_img, (target_width, target_height))
    
    # 转换为RGB并归一化
    left_rgb = cv2.cvtColor(left_resized, cv2.COLOR_BGR2RGB)
    right_rgb = cv2.cvtColor(right_resized, cv2.COLOR_BGR2RGB)
    
    # 归一化到[0,1]
    left_norm = left_rgb.astype(np.float32) / 255.0
    right_norm = right_rgb.astype(np.float32) / 255.0
    
    # 转换为CHW格式并添加batch维度
    left_tensor = np.transpose(left_norm, (2, 0, 1))[np.newaxis, ...]
    right_tensor = np.transpose(right_norm, (2, 0, 1))[np.newaxis, ...]
    
    return left_tensor, right_tensor

def postprocess_disparity(disparity, original_height, original_width):
    """
    后处理视差图
    """
    # 移除batch维度
    if len(disparity.shape) == 4:
        disparity = disparity[0, 0]
    elif len(disparity.shape) == 3:
        disparity = disparity[0]
    
    # 调整回原始尺寸
    disparity_resized = cv2.resize(disparity, (original_width, original_height))
    
    return disparity_resized

def colorize_disparity(disparity):
    """
    将视差图转换为彩色图像用于可视化
    """
    # 归一化到0-255
    disp_norm = cv2.normalize(disparity, None, 0, 255, cv2.NORM_MINMAX)
    disp_norm = disp_norm.astype(np.uint8)
    
    # 应用颜色映射
    disp_color = cv2.applyColorMap(disp_norm, cv2.COLORMAP_JET)
    
    return disp_color

def run_inference(model_path, left_image_path, right_image_path, output_path=None):
    """
    运行LightStereo推理
    """
    print(f"加载模型: {model_path}")
    
    # 创建ONNX Runtime会话
    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
    session = ort.InferenceSession(model_path, providers=providers)
    
    print(f"使用的执行提供者: {session.get_providers()}")
    
    # 获取输入输出信息
    input_names = [input.name for input in session.get_inputs()]
    output_names = [output.name for output in session.get_outputs()]
    
    print(f"输入名称: {input_names}")
    print(f"输出名称: {output_names}")
    
    # 加载图像
    print(f"加载左图像: {left_image_path}")
    print(f"加载右图像: {right_image_path}")
    
    left_img = cv2.imread(left_image_path)
    right_img = cv2.imread(right_image_path)
    
    if left_img is None or right_img is None:
        raise ValueError("无法加载图像文件")
    
    original_height, original_width = left_img.shape[:2]
    print(f"原始图像尺寸: {original_width}x{original_height}")
    
    # 预处理
    print("预处理图像...")
    left_tensor, right_tensor = preprocess_images(left_img, right_img)
    
    print(f"输入张量形状: left={left_tensor.shape}, right={right_tensor.shape}")
    
    # 推理
    print("开始推理...")
    start_time = time.time()
    
    inputs = {input_names[0]: left_tensor, input_names[1]: right_tensor}
    outputs = session.run(output_names, inputs)
    
    inference_time = time.time() - start_time
    print(f"推理时间: {inference_time:.3f}秒")
    
    # 后处理
    print("后处理结果...")
    disparity = outputs[0]
    disparity_final = postprocess_disparity(disparity, original_height, original_width)
    
    print(f"视差图范围: {disparity_final.min():.2f} - {disparity_final.max():.2f}")
    
    # 生成彩色视差图
    disparity_color = colorize_disparity(disparity_final)
    
    # 保存结果
    if output_path is None:
        output_path = "lightstereo_result.png"
    
    cv2.imwrite(output_path, disparity_color)
    print(f"结果已保存到: {output_path}")
    
    # 也保存原始视差图
    disparity_gray_path = output_path.replace('.png', '_gray.png')
    cv2.imwrite(disparity_gray_path, (disparity_final * 255 / disparity_final.max()).astype(np.uint8))
    print(f"灰度视差图已保存到: {disparity_gray_path}")
    
    return disparity_final, disparity_color

def main():
    parser = argparse.ArgumentParser(description='LightStereo ONNX推理')
    parser.add_argument('--model', default='models/lightstereo_s_sceneflow_general_opt_576_960.onnx',
                       help='ONNX模型路径')
    parser.add_argument('--left', default='test_data/left.png',
                       help='左图像路径')
    parser.add_argument('--right', default='test_data/right.png', 
                       help='右图像路径')
    parser.add_argument('--output', default='lightstereo_result_color.png',
                       help='输出图像路径')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.model):
        print(f"错误: 模型文件不存在: {args.model}")
        return
    
    if not os.path.exists(args.left):
        print(f"错误: 左图像文件不存在: {args.left}")
        return
        
    if not os.path.exists(args.right):
        print(f"错误: 右图像文件不存在: {args.right}")
        return
    
    try:
        disparity, disparity_color = run_inference(args.model, args.left, args.right, args.output)
        print("推理完成!")
        
    except Exception as e:
        print(f"推理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
