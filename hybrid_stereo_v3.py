#!/usr/bin/env python3
"""
方法3: 多算法融合
"""

import cv2
import numpy as np
import torch
import torch.nn as nn
import onnxruntime as ort
from sklearn.ensemble import RandomForestRegressor

class MultiAlgorithmFusion:
    """多算法融合类"""
    
    def __init__(self):
        self.algorithms = {}
        self.weights = {}
        self.fusion_model = None
        
    def add_algorithm(self, name, algorithm_func, weight=1.0):
        """添加算法"""
        self.algorithms[name] = algorithm_func
        self.weights[name] = weight
        
    def compute_all_disparities(self, left_img, right_img):
        """计算所有算法的视差图"""
        disparities = {}
        
        for name, algorithm in self.algorithms.items():
            print(f"运行算法: {name}")
            try:
                disp = algorithm(left_img, right_img)
                disparities[name] = disp
            except Exception as e:
                print(f"算法 {name} 失败: {e}")
                disparities[name] = np.zeros((left_img.shape[0], left_img.shape[1]))
        
        return disparities
    
    def simple_weighted_fusion(self, disparities):
        """简单加权融合"""
        
        # 归一化所有视差图到相同范围
        normalized_disparities = {}
        for name, disp in disparities.items():
            if disp.max() > 0:
                normalized_disparities[name] = disp / disp.max()
            else:
                normalized_disparities[name] = disp
        
        # 加权平均
        fused_disparity = np.zeros_like(list(disparities.values())[0])
        total_weight = 0
        
        for name, disp in normalized_disparities.items():
            weight = self.weights[name]
            fused_disparity += weight * disp
            total_weight += weight
        
        if total_weight > 0:
            fused_disparity /= total_weight
        
        return fused_disparity
    
    def confidence_based_fusion(self, left_img, disparities):
        """基于置信度的融合"""
        
        def compute_confidence(img, disparity):
            """计算视差图的置信度"""

            # 转换为灰度图计算梯度
            if len(img.shape) == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                gray = img

            # 基于梯度的置信度
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
            
            # 基于视差平滑性的置信度
            disp_grad_x = cv2.Sobel(disparity, cv2.CV_64F, 1, 0, ksize=3)
            disp_grad_y = cv2.Sobel(disparity, cv2.CV_64F, 0, 1, ksize=3)
            disp_gradient = np.sqrt(disp_grad_x**2 + disp_grad_y**2)
            
            # 综合置信度 (纹理丰富且视差平滑的区域置信度高)
            confidence = gradient_magnitude / (1 + disp_gradient)
            confidence = cv2.GaussianBlur(confidence, (5, 5), 1.0)
            
            return confidence / confidence.max()
        
        # 计算每个算法的置信度
        confidences = {}
        for name, disp in disparities.items():
            confidences[name] = compute_confidence(left_img, disp)
        
        # 基于置信度的像素级融合
        H, W = left_img.shape[:2]
        fused_disparity = np.zeros((H, W))
        
        for y in range(H):
            for x in range(W):
                # 获取该像素点所有算法的置信度
                pixel_confidences = [confidences[name][y, x] for name in disparities.keys()]
                pixel_disparities = [disparities[name][y, x] for name in disparities.keys()]
                
                # 选择置信度最高的算法结果
                best_idx = np.argmax(pixel_confidences)
                fused_disparity[y, x] = pixel_disparities[best_idx]
        
        return fused_disparity
    
    def learned_fusion(self, left_img, disparities, fusion_model=None):
        """学习式融合"""
        
        if fusion_model is None:
            # 使用简单的随机森林作为融合模型
            fusion_model = RandomForestRegressor(n_estimators=50, random_state=42)
            
            # 这里需要训练数据来训练融合模型
            # 特征: 多个算法的视差值 + 图像特征
            # 标签: ground truth视差值
            print("警告: 未提供训练好的融合模型，使用未训练的模型")
        
        # 提取特征
        features = self.extract_fusion_features(left_img, disparities)
        
        # 预测融合结果
        H, W = left_img.shape[:2]
        fused_disparity = np.zeros((H, W))
        
        # 这里简化处理，实际应该批量预测
        for y in range(0, H, 10):  # 降采样以提高速度
            for x in range(0, W, 10):
                pixel_features = features[y, x, :]
                if hasattr(fusion_model, 'predict'):
                    pred = fusion_model.predict([pixel_features])[0]
                else:
                    # 如果没有训练，使用简单平均
                    pred = np.mean([disparities[name][y, x] for name in disparities.keys()])
                
                # 填充邻近区域
                fused_disparity[y:y+10, x:x+10] = pred
        
        return fused_disparity
    
    def extract_fusion_features(self, img, disparities):
        """提取用于融合的特征"""
        
        H, W = img.shape[:2]
        n_algorithms = len(disparities)
        
        # 特征维度: 算法数量 + 图像特征
        feature_dim = n_algorithms + 3  # RGB
        features = np.zeros((H, W, feature_dim))
        
        # 添加各算法的视差值
        for i, (name, disp) in enumerate(disparities.items()):
            features[:, :, i] = disp
        
        # 添加图像特征
        if len(img.shape) == 3:
            features[:, :, n_algorithms:n_algorithms+3] = img / 255.0
        else:
            # 灰度图
            gray_feature = img / 255.0
            features[:, :, n_algorithms] = gray_feature
            features[:, :, n_algorithms+1] = gray_feature
            features[:, :, n_algorithms+2] = gray_feature
        
        return features

# 定义各种算法
def sgbm_algorithm(left_img, right_img):
    """SGBM算法"""
    left_gray = cv2.cvtColor(left_img, cv2.COLOR_BGR2GRAY)
    right_gray = cv2.cvtColor(right_img, cv2.COLOR_BGR2GRAY)
    
    stereo = cv2.StereoSGBM_create(
        minDisparity=0, numDisparities=128, blockSize=11,
        P1=8*3*11**2, P2=32*3*11**2, disp12MaxDiff=1,
        uniquenessRatio=10, speckleWindowSize=100, speckleRange=32
    )
    
    disparity = stereo.compute(left_gray, right_gray).astype(np.float32) / 16.0
    disparity[disparity < 0] = 0
    return disparity

def bm_algorithm(left_img, right_img):
    """Block Matching算法"""
    left_gray = cv2.cvtColor(left_img, cv2.COLOR_BGR2GRAY)
    right_gray = cv2.cvtColor(right_img, cv2.COLOR_BGR2GRAY)
    
    stereo = cv2.StereoBM_create(numDisparities=128, blockSize=15)
    disparity = stereo.compute(left_gray, right_gray).astype(np.float32) / 16.0
    disparity[disparity < 0] = 0
    return disparity

def onnx_lightstereo_algorithm(left_img, right_img):
    """LightStereo ONNX算法"""
    try:
        # 加载ONNX模型
        session = ort.InferenceSession("models/lightstereo_s_sceneflow_general_opt_576_960.onnx",
                                     providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])
        
        # 预处理
        def preprocess(img):
            resized = cv2.resize(img, (960, 576))
            rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
            normalized = rgb.astype(np.float32) / 255.0
            return np.transpose(normalized, (2, 0, 1))[np.newaxis, ...]
        
        left_tensor = preprocess(left_img)
        right_tensor = preprocess(right_img)
        
        # 推理
        inputs = {'left_img': left_tensor, 'right_img': right_tensor}
        outputs = session.run(['disp_pred'], inputs)
        
        # 后处理
        disparity = outputs[0][0, 0]
        disparity = cv2.resize(disparity, (left_img.shape[1], left_img.shape[0]))
        
        return disparity
        
    except Exception as e:
        print(f"LightStereo算法失败: {e}")
        return np.zeros((left_img.shape[0], left_img.shape[1]))

# 使用示例
if __name__ == "__main__":
    # 加载图像
    left = cv2.imread("test/01_left_image.png")
    right = cv2.imread("test/02_right_image.png")
    
    if left is None or right is None:
        print("图像加载失败")
        exit()
    
    print("=== 多算法融合 ===")
    
    # 创建融合器
    fusion = MultiAlgorithmFusion()
    
    # 添加算法
    fusion.add_algorithm("SGBM", sgbm_algorithm, weight=1.0)
    fusion.add_algorithm("BM", bm_algorithm, weight=0.5)
    fusion.add_algorithm("LightStereo", onnx_lightstereo_algorithm, weight=2.0)
    
    # 计算所有算法的结果
    disparities = fusion.compute_all_disparities(left, right)
    
    # 方法1: 简单加权融合
    print("简单加权融合...")
    fused_weighted = fusion.simple_weighted_fusion(disparities)
    cv2.imwrite("fused_weighted.png", 
                (fused_weighted * 255 / fused_weighted.max()).astype(np.uint8))
    
    # 方法2: 基于置信度的融合
    print("基于置信度的融合...")
    fused_confidence = fusion.confidence_based_fusion(left, disparities)
    cv2.imwrite("fused_confidence.png", 
                (fused_confidence * 255 / fused_confidence.max()).astype(np.uint8))
    
    # 保存各个算法的单独结果用于对比
    for name, disp in disparities.items():
        if disp.max() > 0:
            cv2.imwrite(f"disparity_{name}.png", 
                        (disp * 255 / disp.max()).astype(np.uint8))
    
    print("多算法融合完成！")
    print("结果文件:")
    print("- fused_weighted.png (加权融合)")
    print("- fused_confidence.png (置信度融合)")
    print("- disparity_*.png (各算法单独结果)")
