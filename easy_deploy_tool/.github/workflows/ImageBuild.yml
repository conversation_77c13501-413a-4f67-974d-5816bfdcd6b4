name: Test Docker Build

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  nvidia_gpu_trt8_u2004_image_build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Execute build function
      run: |
        docker build -f docker/nvidia_gpu_tensorrt_trt8_u2004.dockerfile .

  nvidia_gpu_trt8_u2204_image_build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Execute build function
      run: |
        docker build -f docker/nvidia_gpu_tensorrt_trt8_u2204.dockerfile .

  nvidia_gpu_trt10_u2204_image_build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Execute build function
      run: |
        docker build -f docker/nvidia_gpu_tensorrt_trt10_u2204.dockerfile .

  jetson_trt8_u2004_image_build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Execute build function
      run: |
        docker build -f docker/jetson_tensorrt_trt8_u2004.dockerfile .

  jetson_trt8_u2204_image_build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Execute build function
      run: |
        docker build -f docker/jetson_tensorrt_trt8_u2204.dockerfile .

  jetson_trt10_u2204_image_build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Execute build function
      run: |
        docker build -f docker/jetson_tensorrt_trt10_u2204.dockerfile .

  rknn_230_u2204_image_build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Execute build function
      run: |
        docker build --platform linux/arm64 -f docker/rknn_230_u2204.dockerfile .
