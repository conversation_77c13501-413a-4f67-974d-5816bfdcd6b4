#基础样式
BasedOnStyle: Google
# 指针和引用的对齐: Left, Right, Middle
DerivePointerAlignment: false
PointerAlignment: Right
#访问修饰符前的空格
AccessModifierOffset: -2
# 缩进宽度
IndentWidth: 2
# 连续的空行保留几行
MaxEmptyLinesToKeep: 1
# 圆括号的换行方式
BreakBeforeBraces: Custom
# 是否允许短方法单行
AllowShortFunctionsOnASingleLine: false
# 支持一行的if
AllowShortIfStatementsOnASingleLine: false
# 在未封闭(括号的开始和结束不在同一行)的括号中的代码是否对齐
AlignAfterOpenBracket: Align
# switch的case缩进
IndentCaseLabels: true
# 每行字符的长度
ColumnLimit: 100
# 注释对齐,true将左对齐，false不会对齐
AlignTrailingComments: true
# C cast括号后加空格
SpaceAfterCStyleCast: false
# 换行的时候对齐操作符
AlignOperands: true
# 中括号两边空格 []
SpacesInSquareBrackets: false
# 多行声明语句按照=对齐
AlignConsecutiveDeclarations: true
# 连续赋值时，对齐所有等号
AlignConsecutiveAssignments: true
# 容器类的空格 例如 OC的字典
SpacesInContainerLiterals: false
# 在构造函数初始化时按逗号断行，并以冒号对齐
BreakConstructorInitializersBeforeComma: false
# 函数参数换行
AllowAllParametersOfDeclarationOnNextLine: true
#在续行(#下一行)时的缩进长度
ContinuationIndentWidth: 4
# tab键盘的宽度
TabWidth: 4
# 赋值运算符前加空格
SpaceBeforeAssignmentOperators: true
# 行尾的注释前加1个空格
SpacesBeforeTrailingComments: 1
# false表示所有形参要么都在同一行，要么都各自一行
BinPackParameters: false
# 头文件排序
SortIncludes: false

# 大括号换行，只有当BreakBeforeBraces设置为Custom时才有效
BraceWrapping:
  # class定义后面
  AfterClass: false
  # 控制语句后面
  AfterControlStatement: true
  # enum定义后面
  AfterEnum: false
  # 函数定义后面
  AfterFunction: true
  # 命名空间定义后面
  AfterNamespace: false
  # struct定义后面
  AfterStruct: false
  # union定义后面
  AfterUnion: false
  # extern之后
  AfterExternBlock: false
  # catch之前
  BeforeCatch: false
  # else之前
  BeforeElse: false
  # 缩进大括号
  IndentBraces: false
  # 分离空函数
  SplitEmptyFunction: false
  # 分离空语句
  SplitEmptyRecord: false
  # 分离空命名空间
  SplitEmptyNamespace: false
