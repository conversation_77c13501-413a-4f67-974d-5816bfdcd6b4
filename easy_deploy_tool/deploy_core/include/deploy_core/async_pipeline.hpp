#pragma once

#include <functional>
#include <future>
#include <memory>
#include <thread>
#include <unordered_map>

#include "deploy_core/async_pipeline_impl.hpp"
#include "deploy_core/blob_buffer.hpp"
#include "common_utils/block_queue.hpp"

namespace easy_deploy {

/**
 * @brief The basic unit pointer which is parsed in the pipeline processing.
 *
 */
class IPipelinePackage {
public:
  /**
   * @brief `GetInferBuffer` provides the interface to get the blobs buffer instance
   * which will be used to deploy inference. Case the algorithm may need multiple inference
   * core and multiple blobs buffer to complete the whole processing.
   *
   * @return BlobsTensor*
   */
  virtual BlobsTensor *GetInferBuffer() = 0;

protected:
  virtual ~IPipelinePackage() = default;
};

/**
 * @brief This base class provides a simple implementation of the asynchronous inference
 * pipeline which could be plug-and-play.
 *
 * `BaseAsyncPipeline` takes function instance as a basic unit `Block` of the pipeline. User should
 * call the static method `BuildPipelineBlock` to construct a `Block`. Multiple `Block`s make up
 * a `Context`, which pipeline deploys the whole process on.
 *
 * @tparam ResultType
 * @tparam GenResult
 */
template <typename ResultType, typename GenResult>
class BaseAsyncPipeline {
  using ParsingType = std::shared_ptr<IPipelinePackage>;
  using Block_t     = AsyncPipelineBlock<ParsingType>;
  using Context_t   = AsyncPipelineContext<ParsingType>;

protected:
  BaseAsyncPipeline() = default;

  ~BaseAsyncPipeline()
  {
    ClosePipeline();
  }

  /**
   * @brief The `Block` in pipeline is constructed with a function and its name. Call this method
   * in the derived class to get `Block_t` instance which is used to configure the whole pipeline.
   *
   * @param func
   * @param block_name
   * @return Block_t
   */
  static Block_t BuildPipelineBlock(const std::function<bool(ParsingType)> &func,
                                    const std::string                      &block_name)
  {
    return Block_t(func, block_name);
  }

  /**
   * @brief Configure the pipelien with a `pipeline_name` and multiple `Context_t` instances. One
   * derived class intance could have sereral pipelines by calling `ConfigPipeline`.
   *
   * @param pipeline_name
   * @param block_list
   */
  void ConfigPipeline(const std::string &pipeline_name, const std::vector<Context_t> &block_list)
  {
    map_name2instance_.emplace(pipeline_name, block_list);
  }

public:
  /**
   * @brief Get the default pipeline context. Multiple instances derived from `BaseAsyncPipeline`
   * could use this method to get the context from the other to generate a more complex pipeline.
   * For example, in detection_2d_yolov8, we combine the algorithm process and inference_core
   * process to make a integral processing pipeline.
   *
   * @return const Context_t&
   */
  const Context_t &GetPipelineContext() const
  {
    if (map_name2instance_.size() != 1)
    {
      throw std::runtime_error("[BaseAsyncPipeline] expect one pipeline, got " +
                               std::to_string(map_name2instance_.size()));
    }
    return map_name2instance_.begin()->second.GetContext();
  }

  /**
   * @brief `PushPipeline` allow user to asynchronously push the package into pipeline and wait on
   * the `future` in another thread. The instance of template type `Result` is generated by functor
   * `GenResult`.
   *
   * @param pipeline_name
   * @param package
   * @return std::future<ResultType>
   */
  [[nodiscard]] std::future<ResultType> PushPipeline(const std::string &pipeline_name,
                                                     const ParsingType &package) noexcept
  {
    if (map_name2instance_.find(pipeline_name) == map_name2instance_.end())
    {
      LOG_ERROR("[BaseAsyncPipeline] `PushPipeline` pipeline {%s} is not valid !!!",
                pipeline_name.c_str());
      return std::future<ResultType>();
    }

    if (!map_name2instance_.at(pipeline_name).IsInitialized())
    {
      LOG_ERROR("[BaseAsyncPipeline] `PushPipeline` pipeline {%s} is not initilized !!!",
                pipeline_name.c_str());
      ;
      return std::future<ResultType>();
    }

    map_index2result_[package_index_] = std::promise<ResultType>();
    auto ret                          = map_index2result_[package_index_].get_future();

    auto callback = [this, package_index = package_index_](const ParsingType &package) -> bool {
      ResultType result = gen_result_from_package_(package);
      map_index2result_[package_index].set_value(std::move(result));
      map_index2result_.erase(package_index);
      return true;
    };
    map_name2instance_[pipeline_name].PushPipeline(package, callback);

    package_index_++;

    return std::move(ret);
  }

  /**
   * @brief Return if the pipeline is initialized.
   *
   * @param pipeline_name
   * @return true
   * @return false
   */
  bool IsPipelineInitialized(const std::string &pipeline_name) noexcept
  {
    if (map_name2instance_.find(pipeline_name) == map_name2instance_.end())
    {
      return false;
    }
    return map_name2instance_[pipeline_name].IsInitialized();
  }

  /**
   * @brief Close all pipeline. The un-finished packages will be dropped.
   *
   */
  void ClosePipeline()
  {
    for (auto &p_name_ins : map_name2instance_)
    {
      p_name_ins.second.ClosePipeline();
    }
  }

  /**
   * @brief Stop all pipeline. The un-finished packages will not be dropped.
   *
   */
  void StopPipeline()
  {
    for (auto &p_name_ins : map_name2instance_)
    {
      p_name_ins.second.StopPipeline();
    }
  }

  /**
   * @brief Initialize all configured pipeline. Call this function before push packages into
   * pipeline.
   *
   */
  void InitPipeline()
  {
    for (auto &p_name_ins : map_name2instance_)
    {
      p_name_ins.second.Init();
    }
  }

private:
  std::unordered_map<std::string, PipelineInstance<ParsingType>> map_name2instance_;

  size_t                                               package_index_ = 0;
  std::unordered_map<size_t, std::promise<ResultType>> map_index2result_;
  GenResult                                            gen_result_from_package_;
};

} // namespace easy_deploy
