#!/usr/bin/env python3
"""
方法1: 传统匹配 + 深度学习后处理
"""

import cv2
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

class DisparityRefinementNet(nn.Module):
    """深度学习后处理网络 - 用于精细化传统算法的视差图"""
    
    def __init__(self):
        super().__init__()
        
        # 编码器 - 提取特征
        self.encoder = nn.Sequential(
            nn.Conv2d(4, 32, 3, padding=1),  # 输入: RGB + 初始视差
            nn.ReLU(),
            nn.Conv2d(32, 64, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(),
        )
        
        # 解码器 - 生成精细化视差
        self.decoder = nn.Sequential(
            nn.Conv2d(128, 64, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(64, 32, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(32, 1, 3, padding=1),  # 输出精细化视差
        )
        
    def forward(self, rgb, initial_disp):
        # 拼接RGB图像和初始视差
        x = torch.cat([rgb, initial_disp], dim=1)
        
        # 编码
        features = self.encoder(x)
        
        # 解码
        refined_disp = self.decoder(features)
        
        # 残差连接 - 在初始视差基础上进行修正
        return initial_disp + refined_disp

def traditional_stereo_matching(left_img, right_img):
    """传统立体匹配算法"""
    
    # 转换为灰度图
    left_gray = cv2.cvtColor(left_img, cv2.COLOR_BGR2GRAY)
    right_gray = cv2.cvtColor(right_img, cv2.COLOR_BGR2GRAY)
    
    # 使用SGBM算法
    stereo = cv2.StereoSGBM_create(
        minDisparity=0,
        numDisparities=128,  # 必须是16的倍数
        blockSize=11,
        P1=8 * 3 * 11**2,
        P2=32 * 3 * 11**2,
        disp12MaxDiff=1,
        uniquenessRatio=10,
        speckleWindowSize=100,
        speckleRange=32,
        preFilterCap=63,
        mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
    )
    
    # 计算视差
    disparity = stereo.compute(left_gray, right_gray).astype(np.float32) / 16.0
    
    # 基本后处理
    disparity[disparity < 0] = 0
    
    return disparity

def preprocess_for_refinement(rgb_img, disparity):
    """为深度学习后处理准备数据"""
    
    # 归一化RGB图像
    rgb_norm = rgb_img.astype(np.float32) / 255.0
    rgb_tensor = torch.from_numpy(rgb_norm.transpose(2, 0, 1)).unsqueeze(0)
    
    # 归一化视差图
    disp_norm = disparity / disparity.max() if disparity.max() > 0 else disparity
    disp_tensor = torch.from_numpy(disp_norm).unsqueeze(0).unsqueeze(0)
    
    return rgb_tensor, disp_tensor

def hybrid_stereo_v1(left_img, right_img, refinement_model=None):
    """混合方法1: 传统匹配 + 深度学习后处理"""
    
    print("步骤1: 传统立体匹配...")
    initial_disparity = traditional_stereo_matching(left_img, right_img)
    
    if refinement_model is None:
        print("未提供精细化模型，返回传统算法结果")
        return initial_disparity
    
    print("步骤2: 深度学习后处理...")
    
    # 准备数据
    rgb_tensor, disp_tensor = preprocess_for_refinement(left_img, initial_disparity)
    
    # 深度学习精细化
    with torch.no_grad():
        refined_disp_tensor = refinement_model(rgb_tensor, disp_tensor)
        refined_disparity = refined_disp_tensor.squeeze().numpy()
    
    # 反归一化
    refined_disparity = refined_disparity * initial_disparity.max()
    
    return initial_disparity, refined_disparity

# 训练数据生成示例
def generate_training_data():
    """生成训练数据的示例代码"""
    
    # 这里需要有真实的立体图像对和ground truth视差图
    # 可以使用SceneFlow、KITTI等数据集
    
    training_samples = []
    
    # 伪代码示例
    for left_img, right_img, gt_disparity in dataset:
        # 生成传统算法的初始视差
        initial_disp = traditional_stereo_matching(left_img, right_img)
        
        # 准备训练样本
        rgb_tensor, init_disp_tensor = preprocess_for_refinement(left_img, initial_disp)
        gt_tensor = torch.from_numpy(gt_disparity).unsqueeze(0).unsqueeze(0)
        
        training_samples.append((rgb_tensor, init_disp_tensor, gt_tensor))
    
    return training_samples

def train_refinement_model():
    """训练精细化模型"""
    
    model = DisparityRefinementNet()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
    criterion = nn.L1Loss()
    
    # 加载训练数据
    training_data = generate_training_data()
    
    for epoch in range(100):
        total_loss = 0
        
        for rgb, init_disp, gt_disp in training_data:
            optimizer.zero_grad()
            
            # 前向传播
            pred_disp = model(rgb, init_disp)
            
            # 计算损失
            loss = criterion(pred_disp, gt_disp)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        print(f"Epoch {epoch}, Loss: {total_loss/len(training_data):.4f}")
    
    return model

# 使用示例
if __name__ == "__main__":
    # 加载图像
    left = cv2.imread("test/01_left_image.png")
    right = cv2.imread("test/02_right_image.png")
    
    # 方法1: 仅传统算法
    print("=== 传统算法 ===")
    traditional_result = traditional_stereo_matching(left, right)
    
    # 保存结果
    cv2.imwrite("traditional_disparity.png", 
                (traditional_result * 255 / traditional_result.max()).astype(np.uint8))
    
    # 方法2: 传统 + 深度学习后处理 (需要训练好的模型)
    # refined_model = train_refinement_model()  # 或加载预训练模型
    # initial, refined = hybrid_stereo_v1(left, right, refined_model)
    
    print("传统算法完成，结果保存为 traditional_disparity.png")
